"""
Менеджер изолированных MCP серверов
"""
import asyncio
import subprocess
import os
import sys
import socket
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
import aiofiles
import logging

from app.models import ServerInfo, ServerConfig

logger = logging.getLogger(__name__)


class IsolatedMCPManager:
    """Менеджер для создания и управления изолированными MCP серверами"""
    
    def __init__(self):
        self.servers: Dict[str, Dict] = {}  # server_id -> server info
        self.port_pool = range(8001, 9000)  # Порты для MCP серверов
        self.used_ports = set()
        # Используем абсолютный путь
        self.base_dir = Path.cwd() / "mcp_servers"
        self.base_dir.mkdir(exist_ok=True)
    
    def _get_free_port(self) -> int:
        """Находит свободный порт"""
        for port in self.port_pool:
            if port not in self.used_ports:
                # Проверяем что порт действительно свободен
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    try:
                        s.bind(('localhost', port))
                        self.used_ports.add(port)
                        return port
                    except OSError:
                        continue
        raise RuntimeError("Нет свободных портов")
    
    def _release_port(self, port: int):
        """Освобождает порт"""
        self.used_ports.discard(port)
    
    async def create_server(self, user_id: str, config: ServerConfig) -> str:
        """Создает новый изолированный MCP сервер"""
        server_id = f"{user_id}_{config['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        server_dir = self.base_dir / server_id
        server_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Создание MCP сервера {server_id}")
        
        try:
            # Генерируем файлы сервера
            await self._generate_server_files(server_dir, config)
            
            # Находим свободный порт
            port = self._get_free_port()
            
            # Запускаем изолированный процесс
            process = await self._start_server_process(server_dir, port, config)
            
            # Ждем старта сервера
            await self._wait_for_server_start(port, timeout=30)
            
            # Сохраняем информацию о сервере
            server_url = f"http://localhost:{port}"
            self.servers[server_id] = {
                "process": process,
                "port": port,
                "status": "running",
                "created_at": datetime.now(),
                "user_id": user_id,
                "config": config,
                "url": server_url,
                "server_dir": server_dir
            }
            
            logger.info(f"MCP сервер {server_id} успешно запущен на порту {port}")
            return server_url
            
        except Exception as e:
            logger.error(f"Ошибка создания сервера {server_id}: {e}")
            # Очищаем ресурсы при ошибке
            if 'port' in locals():
                self._release_port(port)
            raise
    
    async def _generate_server_files(self, server_dir: Path, config: ServerConfig):
        """Генерирует файлы MCP сервера"""
        # Создаем server.py
        server_code = self._build_server_code(config)
        async with aiofiles.open(server_dir / "server.py", "w", encoding="utf-8") as f:
            await f.write(server_code)
        
        # Создаем requirements.txt
        requirements = "mcp>=1.0.0\nuvicorn>=0.24.0\nfastapi>=0.104.0"
        async with aiofiles.open(server_dir / "requirements.txt", "w") as f:
            await f.write(requirements)
        
        # Создаем виртуальное окружение и устанавливаем зависимости
        await self._setup_virtual_env(server_dir)
    
    def _build_server_code(self, config: ServerConfig) -> str:
        """Строит код MCP сервера на основе конфигурации"""
        tools_code = self._generate_tools_code(config["tools"])
        
        return f'''#!/usr/bin/env python3
"""
Автоматически сгенерированный MCP сервер: {config["name"]}
Описание: {config["description"]}
"""
import asyncio
import sys
import os
import logging
from mcp.server.fastmcp import FastMCP

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='[{config["name"]}] %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler('{config["name"]}.log'),
        logging.StreamHandler()
    ]
)

# Создаем MCP сервер
mcp = FastMCP("{config["name"]}")

{tools_code}

@mcp.resource("info://server")
def get_server_info() -> str:
    """Информация о сервере"""
    return f\"\"\"
Сервер: {config["name"]}
Описание: {config["description"]}
Доступные инструменты: {", ".join(config["tools"])}
\"\"\"

if __name__ == "__main__":
    port = int(os.getenv("MCP_SERVER_PORT", 8001))
    logging.info(f"Запуск MCP сервера '{config["name"]}' на порту {{port}}")

    # Запускаем сервер через встроенный метод run
    mcp.run(transport="sse", host="127.0.0.1", port=port)
'''
    
    def _generate_tools_code(self, tools: List[str]) -> str:
        """Генерирует код инструментов"""
        tools_code = ""
        
        for tool in tools:
            if tool.lower() == "calculator":
                tools_code += '''
@mcp.tool()
def calculate(expression: str) -> str:
    """Калькулятор для вычисления математических выражений"""
    try:
        # Безопасное вычисление только математических операций
        allowed_chars = set('0123456789+-*/().')
        if not all(c in allowed_chars or c.isspace() for c in expression):
            return "Ошибка: недопустимые символы в выражении"
        
        result = eval(expression)
        return f"Результат: {result}"
    except Exception as e:
        return f"Ошибка вычисления: {str(e)}"

'''
            elif tool.lower() == "echo":
                tools_code += '''
@mcp.tool()
def echo(message: str) -> str:
    """Эхо - возвращает переданное сообщение"""
    return f"Эхо: {message}"

'''
            elif tool.lower() == "time":
                tools_code += '''
@mcp.tool()
def get_current_time() -> str:
    """Возвращает текущее время"""
    from datetime import datetime
    return f"Текущее время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

'''
            elif tool.lower() == "random":
                tools_code += '''
@mcp.tool()
def random_number(min_val: int = 1, max_val: int = 100) -> str:
    """Генерирует случайное число в заданном диапазоне"""
    import random
    number = random.randint(min_val, max_val)
    return f"Случайное число от {min_val} до {max_val}: {number}"

'''
        
        return tools_code
    
    async def _setup_virtual_env(self, server_dir: Path):
        """Настраивает виртуальное окружение для сервера"""
        venv_dir = server_dir / "venv"

        logger.info(f"Создание виртуального окружения в {venv_dir}")

        # Создаем виртуальное окружение
        process = await asyncio.create_subprocess_exec(
            sys.executable, "-m", "venv", str(venv_dir),
            cwd=server_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()

        if process.returncode != 0:
            logger.error(f"Ошибка создания venv: {stderr.decode()}")
            raise RuntimeError(f"Не удалось создать виртуальное окружение: {stderr.decode()}")

        # Определяем путь к pip в виртуальном окружении
        if os.name == 'nt':  # Windows
            pip_path = venv_dir / "Scripts" / "pip"
        else:  # Unix/Linux
            pip_path = venv_dir / "bin" / "pip"

        logger.info(f"Установка зависимостей через {pip_path}")

        # Устанавливаем зависимости
        process = await asyncio.create_subprocess_exec(
            str(pip_path), "install", "-r", str(server_dir / "requirements.txt"),
            cwd=server_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()

        if process.returncode != 0:
            logger.error(f"Ошибка установки зависимостей: {stderr.decode()}")
            raise RuntimeError(f"Не удалось установить зависимости: {stderr.decode()}")

        logger.info("Виртуальное окружение настроено успешно")
    
    async def _start_server_process(self, server_dir: Path, port: int, config: ServerConfig):
        """Запускает процесс MCP сервера"""
        venv_dir = server_dir / "venv"
        
        # Определяем путь к python в виртуальном окружении
        if os.name == 'nt':  # Windows
            python_path = venv_dir / "Scripts" / "python"
        else:  # Unix/Linux
            python_path = venv_dir / "bin" / "python"
        
        env = {
            **os.environ,
            "MCP_SERVER_PORT": str(port),
            "MCP_SERVER_NAME": config["name"]
        }
        
        process = await asyncio.create_subprocess_exec(
            str(python_path), "server.py",
            cwd=server_dir,
            env=env,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        return process
    
    async def _wait_for_server_start(self, port: int, timeout: int = 30):
        """Ждет запуска сервера на указанном порту"""
        import aiohttp
        
        for _ in range(timeout):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"http://localhost:{port}/health",
                        timeout=aiohttp.ClientTimeout(total=2)
                    ) as response:
                        if response.status == 200:
                            return
            except:
                pass
            
            await asyncio.sleep(1)
        
        raise TimeoutError(f"Сервер не запустился на порту {port} за {timeout} секунд")
    
    async def delete_server(self, server_id: str):
        """Удаляет MCP сервер"""
        if server_id not in self.servers:
            raise ValueError(f"Сервер {server_id} не найден")
        
        info = self.servers[server_id]
        
        # Останавливаем процесс
        try:
            info["process"].terminate()
            await asyncio.wait_for(info["process"].wait(), timeout=5.0)
        except asyncio.TimeoutError:
            info["process"].kill()
            await info["process"].wait()
        
        # Освобождаем порт
        self._release_port(info["port"])
        
        # Удаляем из списка
        del self.servers[server_id]
        
        logger.info(f"Сервер {server_id} удален")
    
    async def get_all_servers_info(self) -> List[ServerInfo]:
        """Возвращает информацию о всех серверах"""
        servers_info = []
        
        for server_id, info in self.servers.items():
            server_info = ServerInfo(
                server_id=server_id,
                user_id=info["user_id"],
                name=info["config"]["name"],
                description=info["config"]["description"],
                port=info["port"],
                status=info["status"],
                created_at=info["created_at"],
                url=info["url"],
                tools=info["config"]["tools"]
            )
            servers_info.append(server_info)
        
        return servers_info
    
    async def cleanup_all_servers(self):
        """Останавливает все серверы при завершении работы"""
        logger.info("Остановка всех MCP серверов...")
        
        for server_id in list(self.servers.keys()):
            try:
                await self.delete_server(server_id)
            except Exception as e:
                logger.error(f"Ошибка остановки сервера {server_id}: {e}")
        
        logger.info("Все серверы остановлены")
