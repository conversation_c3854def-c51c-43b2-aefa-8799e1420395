"""
Главный модуль FastAPI приложения для MCP SaaS
"""
import asyncio
from contextlib import asynccontextmanager
from pathlib import Path
from fastapi import FastAPI, Request, Form, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
import logging

from app.docker_mcp_manager import DockerMCPManager
from app.docker_health_checker import Docker<PERSON>ealthChecker
from app.models import CreateServerRequest

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Инициализация Docker менеджера MCP серверов
mcp_manager = DockerMCPManager()
health_checker = DockerHealthChecker(mcp_manager)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Управление жизненным циклом приложения"""
    # Startup
    logger.info("Запуск MCP SaaS сервиса...")

    # Создаем директории если их нет
    Path("mcp_servers").mkdir(exist_ok=True)
    Path("static").mkdir(exist_ok=True)
    Path("templates").mkdir(exist_ok=True)

    # Запускаем мониторинг в фоне
    monitoring_task = asyncio.create_task(health_checker.start_monitoring())
    logger.info("Мониторинг MCP серверов запущен")

    yield

    # Shutdown
    logger.info("Остановка MCP SaaS сервиса...")
    monitoring_task.cancel()
    await mcp_manager.cleanup_all_servers()


# Создание FastAPI приложения
app = FastAPI(
    title="MCP SaaS",
    description="Alpha SaaS Service with Isolated MCP Servers",
    version="0.1.0",
    lifespan=lifespan
)

# Настройка шаблонов и статических файлов
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="static"), name="static")


@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Главная страница"""
    return templates.TemplateResponse("index.html", {
        "request": request,
        "title": "MCP SaaS - Создайте свой MCP сервер"
    })


@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Дашборд с активными серверами"""
    servers = await mcp_manager.get_all_servers_info()
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "title": "Дашборд серверов",
        "servers": servers
    })


@app.post("/api/servers")
async def create_server(
    name: str = Form(...),
    description: str = Form(...),
    tools: str = Form(...)
):
    """Создание нового MCP сервера"""
    try:
        # Парсим инструменты из формы
        tools_list = [tool.strip() for tool in tools.split(",") if tool.strip()]
        
        config = {
            "name": name,
            "description": description,
            "tools": tools_list
        }
        
        # Создаем уникальный ID пользователя (в реальном проекте из сессии)
        user_id = f"user_{hash(name) % 10000}"
        
        url = await mcp_manager.create_server(user_id, config)
        
        return JSONResponse({
            "success": True,
            "url": url,
            "message": f"Сервер '{name}' успешно создан!"
        })
        
    except Exception as e:
        logger.error(f"Ошибка создания сервера: {e}")
        raise HTTPException(500, f"Ошибка создания сервера: {str(e)}")


@app.delete("/api/servers/{server_id}")
async def delete_server(server_id: str):
    """Удаление MCP сервера"""
    try:
        await mcp_manager.delete_server(server_id)
        return JSONResponse({
            "success": True,
            "message": f"Сервер {server_id} удален"
        })
    except Exception as e:
        logger.error(f"Ошибка удаления сервера: {e}")
        raise HTTPException(500, f"Ошибка удаления сервера: {str(e)}")


@app.get("/api/servers")
async def list_servers():
    """Список всех серверов"""
    servers = await mcp_manager.get_all_servers_info()
    return JSONResponse({"servers": servers})


@app.get("/health")
async def health_check():
    """Проверка здоровья сервиса"""
    health_status = await health_checker.get_health_status()
    return JSONResponse({
        "status": "healthy",
        "active_servers": len(mcp_manager.servers),
        "version": "0.1.0",
        "health_details": health_status
    })


@app.get("/api/docker/stats")
async def docker_stats():
    """Статистика Docker"""
    stats = mcp_manager.get_docker_stats()
    return JSONResponse({"docker_stats": stats})


@app.get("/api/servers/{server_id}/logs")
async def get_server_logs(server_id: str, lines: int = 50):
    """Получить логи сервера"""
    logs = await health_checker.get_container_logs(server_id, lines)
    return JSONResponse({"logs": logs})


@app.post("/api/docker/cleanup")
async def cleanup_docker():
    """Очистка потерянных контейнеров"""
    try:
        await health_checker.cleanup_orphaned_containers()
        return JSONResponse({
            "success": True,
            "message": "Очистка Docker завершена"
        })
    except Exception as e:
        logger.error(f"Ошибка очистки Docker: {e}")
        raise HTTPException(500, f"Ошибка очистки: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
