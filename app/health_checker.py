"""
Система мониторинга и автоперезапуска MCP серверов
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict
import aiohttp

logger = logging.getLogger(__name__)


class HealthChecker:
    """Мониторинг здоровья MCP серверов"""
    
    def __init__(self, mcp_manager):
        self.mcp_manager = mcp_manager
        self.check_interval = 30  # Проверка каждые 30 секунд
        self.restart_attempts: Dict[str, int] = {}
        self.max_restart_attempts = 3
    
    async def start_monitoring(self):
        """Запускает мониторинг в фоновом режиме"""
        logger.info("Запуск мониторинга MCP серверов")
        
        while True:
            try:
                await self._check_all_servers()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Ошибка в мониторинге: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def _check_all_servers(self):
        """Проверяет все активные серверы"""
        for server_id, info in list(self.mcp_manager.servers.items()):
            try:
                is_healthy = await self._is_server_healthy(info)
                
                if not is_healthy:
                    logger.warning(f"Сервер {server_id} не отвечает")
                    await self._handle_unhealthy_server(server_id, info)
                else:
                    # Сбрасываем счетчик попыток перезапуска при успешной проверке
                    self.restart_attempts.pop(server_id, None)
                    
            except Exception as e:
                logger.error(f"Ошибка проверки сервера {server_id}: {e}")
    
    async def _is_server_healthy(self, server_info: Dict) -> bool:
        """Проверяет здоровье конкретного сервера"""
        try:
            # Проверяем что процесс жив
            if server_info["process"].returncode is not None:
                logger.warning(f"Процесс сервера завершился с кодом {server_info['process'].returncode}")
                return False
            
            # Проверяем что сервер отвечает на HTTP запросы
            port = server_info["port"]
            
            async with aiohttp.ClientSession() as session:
                # Пробуем подключиться к серверу
                async with session.get(
                    f"http://localhost:{port}/",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    # Любой HTTP ответ считаем признаком жизни
                    return response.status < 500
                    
        except aiohttp.ClientError:
            # Сервер не отвечает
            return False
        except Exception as e:
            logger.error(f"Неожиданная ошибка при проверке здоровья: {e}")
            return False
    
    async def _handle_unhealthy_server(self, server_id: str, server_info: Dict):
        """Обрабатывает нездоровый сервер"""
        attempts = self.restart_attempts.get(server_id, 0)
        
        if attempts >= self.max_restart_attempts:
            logger.error(f"Сервер {server_id} превысил максимальное количество попыток перезапуска ({self.max_restart_attempts})")
            await self._mark_server_failed(server_id)
            return
        
        logger.info(f"Попытка перезапуска сервера {server_id} (попытка {attempts + 1}/{self.max_restart_attempts})")
        
        try:
            await self._restart_server(server_id, server_info)
            self.restart_attempts[server_id] = attempts + 1
            logger.info(f"Сервер {server_id} успешно перезапущен")
            
        except Exception as e:
            logger.error(f"Ошибка перезапуска сервера {server_id}: {e}")
            self.restart_attempts[server_id] = attempts + 1
    
    async def _restart_server(self, server_id: str, server_info: Dict):
        """Перезапускает сервер"""
        # Останавливаем старый процесс
        try:
            server_info["process"].terminate()
            await asyncio.wait_for(server_info["process"].wait(), timeout=5.0)
        except asyncio.TimeoutError:
            server_info["process"].kill()
            await server_info["process"].wait()
        
        # Запускаем новый процесс
        new_process = await self.mcp_manager._start_server_process(
            server_info["server_dir"],
            server_info["port"],
            server_info["config"]
        )
        
        # Обновляем информацию о процессе
        server_info["process"] = new_process
        server_info["status"] = "restarted"
        
        # Ждем запуска
        await self.mcp_manager._wait_for_server_start(server_info["port"], timeout=30)
    
    async def _mark_server_failed(self, server_id: str):
        """Помечает сервер как неработающий"""
        if server_id in self.mcp_manager.servers:
            self.mcp_manager.servers[server_id]["status"] = "failed"
            logger.error(f"Сервер {server_id} помечен как неработающий")
    
    async def get_health_status(self) -> Dict:
        """Возвращает общий статус здоровья всех серверов"""
        total_servers = len(self.mcp_manager.servers)
        healthy_servers = 0
        failed_servers = 0
        
        for server_id, info in self.mcp_manager.servers.items():
            if info["status"] == "failed":
                failed_servers += 1
            elif await self._is_server_healthy(info):
                healthy_servers += 1
        
        return {
            "total_servers": total_servers,
            "healthy_servers": healthy_servers,
            "failed_servers": failed_servers,
            "restart_attempts": dict(self.restart_attempts),
            "last_check": datetime.now().isoformat()
        }
