"""
Docker-based MCP Server Manager
Каждый MCP сервер запускается в отдельном Docker контейнере
"""
import asyncio
import docker
import os
import socket
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
import aiofiles
import logging
import aiohttp

from app.models import ServerInfo, ServerConfig

logger = logging.getLogger(__name__)


class DockerMCPManager:
    """Менеджер для создания и управления MCP серверами в Docker контейнерах"""
    
    def __init__(self):
        self.servers: Dict[str, Dict] = {}  # server_id -> server info
        self.port_pool = range(8001, 9000)  # Порты для MCP серверов
        self.used_ports = set()
        # Используем временную директорию вне проекта
        self.base_dir = Path("/tmp") / "mcp_servers"
        self.base_dir.mkdir(exist_ok=True)
        
        # Инициализируем Docker клиент
        try:
            self.docker_client = docker.from_env()
            # Проверяем что Docker работает
            self.docker_client.ping()
            logger.info("Docker клиент успешно подключен")
        except Exception as e:
            logger.error(f"Ошибка подключения к Docker: {e}")
            raise RuntimeError(f"Docker недоступен: {e}")
    
    def _get_free_port(self) -> int:
        """Находит свободный порт"""
        for port in self.port_pool:
            if port not in self.used_ports:
                # Проверяем что порт действительно свободен
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    try:
                        s.bind(('localhost', port))
                        self.used_ports.add(port)
                        return port
                    except OSError:
                        continue
        raise RuntimeError("Нет свободных портов")
    
    def _release_port(self, port: int):
        """Освобождает порт"""
        self.used_ports.discard(port)
    
    async def create_server(self, user_id: str, config: ServerConfig) -> str:
        """Создает новый MCP сервер в Docker контейнере"""
        server_id = f"{user_id}_{config['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        server_dir = self.base_dir / server_id
        server_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Создание MCP сервера {server_id} в Docker контейнере")
        
        try:
            # Генерируем файлы сервера
            await self._generate_server_files(server_dir, config)
            
            # Находим свободный порт
            port = self._get_free_port()
            
            # Создаем и запускаем Docker контейнер
            container = await self._create_and_start_container(server_dir, server_id, port, config)
            
            # Ждем старта сервера
            await self._wait_for_server_start(port, timeout=60)
            
            # Сохраняем информацию о сервере
            server_url = f"http://localhost:{port}"
            self.servers[server_id] = {
                "container": container,
                "container_id": container.id,
                "port": port,
                "status": "running",
                "created_at": datetime.now(),
                "user_id": user_id,
                "config": config,
                "url": server_url,
                "server_dir": server_dir
            }
            
            logger.info(f"MCP сервер {server_id} успешно запущен в контейнере {container.id[:12]} на порту {port}")
            return server_url
            
        except Exception as e:
            logger.error(f"Ошибка создания сервера {server_id}: {e}")
            # Очищаем ресурсы при ошибке
            if 'port' in locals():
                self._release_port(port)
            raise
    
    async def _generate_server_files(self, server_dir: Path, config: ServerConfig):
        """Генерирует файлы для Docker контейнера"""
        # Создаем server.py
        server_code = self._build_server_code(config)
        async with aiofiles.open(server_dir / "server.py", "w", encoding="utf-8") as f:
            await f.write(server_code)
        
        # Создаем requirements.txt
        requirements = "mcp[cli]>=1.0.0\nuvicorn[standard]>=0.24.0\nfastapi>=0.104.0"
        async with aiofiles.open(server_dir / "requirements.txt", "w") as f:
            await f.write(requirements)
        
        # Создаем Dockerfile
        dockerfile_content = self._build_dockerfile(config)
        async with aiofiles.open(server_dir / "Dockerfile", "w") as f:
            await f.write(dockerfile_content)
        
        # Создаем .dockerignore
        dockerignore = """
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.log
.git/
.gitignore
README.md
"""
        async with aiofiles.open(server_dir / ".dockerignore", "w") as f:
            await f.write(dockerignore.strip())
    
    def _build_dockerfile(self, config: ServerConfig) -> str:
        """Создает Dockerfile для MCP сервера"""
        return f'''# Dockerfile для MCP сервера: {config["name"]}
FROM python:3.11-slim

# Устанавливаем рабочую директорию
WORKDIR /app

# Копируем файлы зависимостей
COPY requirements.txt .

# Устанавливаем зависимости
RUN pip install --no-cache-dir -r requirements.txt

# Копируем код сервера
COPY server.py .

# Открываем порт
EXPOSE 3000

# Устанавливаем переменные окружения
ENV PYTHONUNBUFFERED=1
ENV MCP_SERVER_NAME="{config["name"]}"
ENV MCP_SERVER_PORT=3000

# Запускаем сервер
CMD ["python", "server.py"]
'''
    
    def _build_server_code(self, config: ServerConfig) -> str:
        """Строит код MCP сервера"""
        tools_code = self._generate_tools_code(config["tools"])
        
        return f'''#!/usr/bin/env python3
"""
Автоматически сгенерированный MCP сервер: {config["name"]}
Описание: {config["description"]}
Запускается в Docker контейнере
"""
import asyncio
import sys
import os
import logging
from mcp.server.fastmcp import FastMCP

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='[{config["name"]}] %(levelname)s: %(message)s',
    handlers=[logging.StreamHandler()]
)

logger = logging.getLogger(__name__)

# Создаем MCP сервер
mcp = FastMCP("{config["name"]}")

{tools_code}

@mcp.resource("info://server")
def get_server_info() -> str:
    """Информация о сервере"""
    return f\"\"\"
Сервер: {config["name"]}
Описание: {config["description"]}
Доступные инструменты: {", ".join(config["tools"])}
Контейнер: {{os.getenv("HOSTNAME", "unknown")}}
\"\"\"

@mcp.resource("health://check")
def health_check() -> str:
    """Проверка здоровья сервера"""
    return "OK"

if __name__ == "__main__":
    port = int(os.getenv("MCP_SERVER_PORT", 3000))
    logger.info(f"Запуск MCP сервера '{config["name"]}' на порту {{port}} в контейнере")

    # Запускаем сервер (FastMCP.run не поддерживает host параметр)
    mcp.run(transport="sse", port=port)
'''
    
    def _generate_tools_code(self, tools: List[str]) -> str:
        """Генерирует код инструментов"""
        tools_code = ""
        
        for tool in tools:
            if tool.lower() == "calculator":
                tools_code += '''
@mcp.tool()
def calculate(expression: str) -> str:
    """Калькулятор для вычисления математических выражений"""
    try:
        # Безопасное вычисление только математических операций
        allowed_chars = set('0123456789+-*/().')
        if not all(c in allowed_chars or c.isspace() for c in expression):
            return "Ошибка: недопустимые символы в выражении"
        
        result = eval(expression)
        return f"Результат: {result}"
    except Exception as e:
        return f"Ошибка вычисления: {str(e)}"

'''
            elif tool.lower() == "echo":
                tools_code += '''
@mcp.tool()
def echo(message: str) -> str:
    """Эхо - возвращает переданное сообщение"""
    return f"Эхо из контейнера: {message}"

'''
            elif tool.lower() == "time":
                tools_code += '''
@mcp.tool()
def get_current_time() -> str:
    """Возвращает текущее время"""
    from datetime import datetime
    return f"Текущее время в контейнере: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

'''
            elif tool.lower() == "random":
                tools_code += '''
@mcp.tool()
def random_number(min_val: int = 1, max_val: int = 100) -> str:
    """Генерирует случайное число в заданном диапазоне"""
    import random
    number = random.randint(min_val, max_val)
    return f"Случайное число от {min_val} до {max_val}: {number}"

'''
        
        return tools_code
    
    async def _create_and_start_container(self, server_dir: Path, server_id: str, port: int, config: ServerConfig):
        """Создает и запускает Docker контейнер"""
        logger.info(f"Создание Docker образа для {server_id}")
        
        # Создаем Docker образ
        try:
            logger.info(f"Начинаем сборку Docker образа в {server_dir}")
            image, build_logs = self.docker_client.images.build(
                path=str(server_dir),
                tag=f"mcp-server-{server_id.lower()}",
                rm=True,
                forcerm=True
            )

            # Логируем процесс сборки
            for log in build_logs:
                if 'stream' in log:
                    logger.info(f"Docker build: {log['stream'].strip()}")
                elif 'error' in log:
                    logger.error(f"Docker build error: {log['error']}")

            logger.info(f"Docker образ создан: {image.id[:12]}")

        except Exception as e:
            logger.error(f"Ошибка создания Docker образа: {e}")
            raise
        
        # Запускаем контейнер
        try:
            container = self.docker_client.containers.run(
                image.id,
                name=f"mcp-server-{server_id}",
                ports={'3000/tcp': port},
                environment={
                    'MCP_SERVER_PORT': '3000',
                    'MCP_SERVER_NAME': config["name"]
                },
                detach=True,
                remove=False,  # Не удаляем автоматически для отладки
                restart_policy={"Name": "unless-stopped"}
            )
            
            logger.info(f"Docker контейнер запущен: {container.id[:12]}")
            return container
            
        except Exception as e:
            logger.error(f"Ошибка запуска Docker контейнера: {e}")
            raise
    
    async def _wait_for_server_start(self, port: int, timeout: int = 60):
        """Ждет запуска сервера на указанном порту"""
        logger.info(f"Ожидание запуска сервера на порту {port}")
        
        for attempt in range(timeout):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"http://localhost:{port}/",
                        timeout=aiohttp.ClientTimeout(total=3)
                    ) as response:
                        if response.status < 500:
                            logger.info(f"Сервер запустился на порту {port}")
                            return
            except Exception as e:
                if attempt % 10 == 0:  # Логируем каждые 10 попыток
                    logger.debug(f"Попытка {attempt + 1}/{timeout}: {e}")
            
            await asyncio.sleep(1)
        
        raise TimeoutError(f"Сервер не запустился на порту {port} за {timeout} секунд")
    
    async def delete_server(self, server_id: str):
        """Удаляет MCP сервер и его Docker контейнер"""
        if server_id not in self.servers:
            raise ValueError(f"Сервер {server_id} не найден")
        
        info = self.servers[server_id]
        
        try:
            # Останавливаем и удаляем контейнер
            container = info["container"]
            container.stop(timeout=10)
            container.remove()
            logger.info(f"Docker контейнер {container.id[:12]} остановлен и удален")
            
            # Удаляем образ
            try:
                image_tag = f"mcp-server-{server_id.lower()}"
                self.docker_client.images.remove(image_tag, force=True)
                logger.info(f"Docker образ {image_tag} удален")
            except Exception as e:
                logger.warning(f"Не удалось удалить образ: {e}")
            
        except Exception as e:
            logger.error(f"Ошибка удаления контейнера: {e}")
        
        # Освобождаем порт
        self._release_port(info["port"])
        
        # Удаляем из списка
        del self.servers[server_id]
        
        logger.info(f"Сервер {server_id} удален")
    
    async def get_all_servers_info(self) -> List[ServerInfo]:
        """Возвращает информацию о всех серверах"""
        servers_info = []
        
        for server_id, info in self.servers.items():
            # Проверяем статус контейнера
            try:
                container = info["container"]
                container.reload()
                status = container.status
            except:
                status = "unknown"
            
            server_info = ServerInfo(
                server_id=server_id,
                user_id=info["user_id"],
                name=info["config"]["name"],
                description=info["config"]["description"],
                port=info["port"],
                status=status,
                created_at=info["created_at"],
                url=info["url"],
                tools=info["config"]["tools"]
            )
            servers_info.append(server_info)
        
        return servers_info
    
    async def cleanup_all_servers(self):
        """Останавливает все серверы при завершении работы"""
        logger.info("Остановка всех MCP серверов...")
        
        for server_id in list(self.servers.keys()):
            try:
                await self.delete_server(server_id)
            except Exception as e:
                logger.error(f"Ошибка остановки сервера {server_id}: {e}")
        
        logger.info("Все серверы остановлены")
    
    def get_docker_stats(self) -> Dict:
        """Возвращает статистику Docker"""
        try:
            containers = self.docker_client.containers.list(all=True, filters={"name": "mcp-server-"})
            images = self.docker_client.images.list(filters={"reference": "mcp-server-*"})
            
            return {
                "containers_total": len(containers),
                "containers_running": len([c for c in containers if c.status == "running"]),
                "images_total": len(images),
                "docker_version": self.docker_client.version()["Version"]
            }
        except Exception as e:
            logger.error(f"Ошибка получения статистики Docker: {e}")
            return {"error": str(e)}
