"""
Система мониторинга Docker-based MCP серверов
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict
import aiohttp

logger = logging.getLogger(__name__)


class DockerHealthChecker:
    """Мониторинг здоровья MCP серверов в Docker контейнерах"""
    
    def __init__(self, docker_manager):
        self.docker_manager = docker_manager
        self.check_interval = 30  # Проверка каждые 30 секунд
        self.restart_attempts: Dict[str, int] = {}
        self.max_restart_attempts = 3
    
    async def start_monitoring(self):
        """Запускает мониторинг в фоновом режиме"""
        logger.info("Запуск мониторинга Docker MCP серверов")
        
        while True:
            try:
                await self._check_all_servers()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Ошибка в мониторинге: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def _check_all_servers(self):
        """Проверяет все активные серверы"""
        for server_id, info in list(self.docker_manager.servers.items()):
            try:
                is_healthy = await self._is_server_healthy(info)
                
                if not is_healthy:
                    logger.warning(f"Сервер {server_id} не отвечает")
                    await self._handle_unhealthy_server(server_id, info)
                else:
                    # Сбрасываем счетчик попыток перезапуска при успешной проверке
                    self.restart_attempts.pop(server_id, None)
                    
            except Exception as e:
                logger.error(f"Ошибка проверки сервера {server_id}: {e}")
    
    async def _is_server_healthy(self, server_info: Dict) -> bool:
        """Проверяет здоровье конкретного сервера"""
        try:
            # Проверяем статус Docker контейнера
            container = server_info["container"]
            container.reload()
            
            if container.status != "running":
                logger.warning(f"Контейнер {container.id[:12]} не запущен: {container.status}")
                return False
            
            # Проверяем что сервер отвечает на HTTP запросы
            port = server_info["port"]
            
            async with aiohttp.ClientSession() as session:
                # Пробуем подключиться к серверу
                async with session.get(
                    f"http://localhost:{port}/",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    # Любой HTTP ответ считаем признаком жизни
                    return response.status < 500
                    
        except aiohttp.ClientError:
            # Сервер не отвечает
            return False
        except Exception as e:
            logger.error(f"Неожиданная ошибка при проверке здоровья: {e}")
            return False
    
    async def _handle_unhealthy_server(self, server_id: str, server_info: Dict):
        """Обрабатывает нездоровый сервер"""
        attempts = self.restart_attempts.get(server_id, 0)
        
        if attempts >= self.max_restart_attempts:
            logger.error(f"Сервер {server_id} превысил максимальное количество попыток перезапуска ({self.max_restart_attempts})")
            await self._mark_server_failed(server_id)
            return
        
        logger.info(f"Попытка перезапуска сервера {server_id} (попытка {attempts + 1}/{self.max_restart_attempts})")
        
        try:
            await self._restart_server(server_id, server_info)
            self.restart_attempts[server_id] = attempts + 1
            logger.info(f"Сервер {server_id} успешно перезапущен")
            
        except Exception as e:
            logger.error(f"Ошибка перезапуска сервера {server_id}: {e}")
            self.restart_attempts[server_id] = attempts + 1
    
    async def _restart_server(self, server_id: str, server_info: Dict):
        """Перезапускает Docker контейнер"""
        container = server_info["container"]
        
        try:
            # Останавливаем контейнер
            logger.info(f"Остановка контейнера {container.id[:12]}")
            container.stop(timeout=10)
            
            # Запускаем заново
            logger.info(f"Запуск контейнера {container.id[:12]}")
            container.start()
            
            # Обновляем статус
            server_info["status"] = "restarted"
            
            # Ждем запуска
            await self.docker_manager._wait_for_server_start(server_info["port"], timeout=60)
            
        except Exception as e:
            logger.error(f"Ошибка перезапуска контейнера: {e}")
            raise
    
    async def _mark_server_failed(self, server_id: str):
        """Помечает сервер как неработающий"""
        if server_id in self.docker_manager.servers:
            self.docker_manager.servers[server_id]["status"] = "failed"
            logger.error(f"Сервер {server_id} помечен как неработающий")
    
    async def get_health_status(self) -> Dict:
        """Возвращает общий статус здоровья всех серверов"""
        total_servers = len(self.docker_manager.servers)
        healthy_servers = 0
        failed_servers = 0
        
        for server_id, info in self.docker_manager.servers.items():
            if info["status"] == "failed":
                failed_servers += 1
            elif await self._is_server_healthy(info):
                healthy_servers += 1
        
        # Добавляем статистику Docker
        docker_stats = self.docker_manager.get_docker_stats()
        
        return {
            "total_servers": total_servers,
            "healthy_servers": healthy_servers,
            "failed_servers": failed_servers,
            "restart_attempts": dict(self.restart_attempts),
            "last_check": datetime.now().isoformat(),
            "docker_stats": docker_stats
        }
    
    async def cleanup_orphaned_containers(self):
        """Очищает потерянные контейнеры MCP серверов"""
        try:
            # Находим все контейнеры с префиксом mcp-server-
            all_containers = self.docker_manager.docker_client.containers.list(
                all=True, 
                filters={"name": "mcp-server-"}
            )
            
            # Получаем список активных серверов
            active_container_ids = {
                info["container_id"] for info in self.docker_manager.servers.values()
            }
            
            # Удаляем потерянные контейнеры
            for container in all_containers:
                if container.id not in active_container_ids:
                    logger.info(f"Удаление потерянного контейнера: {container.id[:12]}")
                    try:
                        container.stop(timeout=5)
                        container.remove()
                    except Exception as e:
                        logger.warning(f"Ошибка удаления контейнера {container.id[:12]}: {e}")
            
            # Очищаем неиспользуемые образы
            self.docker_manager.docker_client.images.prune(
                filters={"reference": "mcp-server-*"}
            )
            
        except Exception as e:
            logger.error(f"Ошибка очистки потерянных контейнеров: {e}")
    
    async def get_container_logs(self, server_id: str, lines: int = 50) -> str:
        """Возвращает логи контейнера"""
        if server_id not in self.docker_manager.servers:
            return "Сервер не найден"
        
        try:
            container = self.docker_manager.servers[server_id]["container"]
            logs = container.logs(tail=lines, timestamps=True).decode('utf-8')
            return logs
        except Exception as e:
            return f"Ошибка получения логов: {e}"
