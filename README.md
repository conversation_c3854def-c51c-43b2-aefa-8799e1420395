# MCP SaaS - Альфа версия

Простой SaaS сервис для создания и управления изолированными MCP (Model Context Protocol) серверами.

## 🚀 Возможности

- **Быстрое развертывание** - создание MCP серверов за секунды
- **Docker изоляция** - каждый сервер в отдельном Docker контейнере
- **Автомониторинг** - автоматическое отслеживание и перезапуск контейнеров
- **Простой интерфейс** - веб-интерфейс на Bootstrap без сложного ЛК
- **API управление** - REST API для программного управления
- **Автоочистка** - удаление неиспользуемых образов и контейнеров

## 🛠 Технологический стек

- **Backend**: FastAPI + asyncio
- **MCP SDK**: Официальный Python SDK
- **Frontend**: Bootstrap 5 + Vanilla JS
- **Контейнеризация**: Docker для каждого MCP сервера
- **Мониторинг**: Docker API + health checks
- **Изоляция**: Docker контейнеры

## 📦 Установка

### Требования:
- Python 3.9+
- Docker и Docker Compose
- Linux/macOS (рекомендуется)

1. **Клонируйте репозиторий:**
```bash
git clone <repo-url>
cd python-mcp-saas
```

2. **Убедитесь что Docker запущен:**
```bash
docker --version
docker ps
```

3. **Создайте виртуальное окружение:**
```bash
python3 -m venv venv
source venv/bin/activate
```

4. **Установите зависимости:**
```bash
pip install -r requirements.txt
```

5. **Запустите сервер:**
```bash
python -m app.main
```

Сервис будет доступен по адресу: http://localhost:8000

## 🎯 Использование

### Веб-интерфейс

1. Откройте http://localhost:8000
2. Заполните форму создания сервера:
   - **Название** - уникальное имя сервера
   - **Описание** - краткое описание функций
   - **Инструменты** - список через запятую (calculator, echo, time, random)
3. Нажмите "Создать сервер"
4. Получите URL вашего MCP сервера

### API

**Создание сервера:**
```bash
curl -X POST http://localhost:8000/api/servers \
  -F "name=test-server" \
  -F "description=Тестовый сервер" \
  -F "tools=calculator,echo"
```

**Список серверов:**
```bash
curl http://localhost:8000/api/servers
```

**Удаление сервера:**
```bash
curl -X DELETE http://localhost:8000/api/servers/{server_id}
```

**Проверка здоровья:**
```bash
curl http://localhost:8000/health
```

**Статистика Docker:**
```bash
curl http://localhost:8000/api/docker/stats
```

**Логи сервера:**
```bash
curl http://localhost:8000/api/servers/{server_id}/logs
```

**Очистка Docker:**
```bash
curl -X POST http://localhost:8000/api/docker/cleanup
```

## 🔧 Доступные инструменты

- **calculator** - математические вычисления
- **echo** - возврат переданного сообщения  
- **time** - текущее время
- **random** - генерация случайных чисел

## 📁 Структура проекта

```
python-mcp-saas/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI приложение
│   ├── models.py                  # Модели данных
│   ├── docker_mcp_manager.py      # Docker менеджер MCP серверов
│   ├── docker_health_checker.py   # Docker система мониторинга
│   ├── mcp_manager.py             # Старый venv менеджер (deprecated)
│   └── health_checker.py          # Старый мониторинг (deprecated)
├── templates/
│   ├── base.html                 # Базовый шаблон
│   ├── index.html                # Главная страница
│   └── dashboard.html            # Дашборд серверов
├── static/                       # Статические файлы
├── mcp_servers/                  # Директория серверов (Docker контексты)
├── requirements.txt              # Зависимости Python
└── README.md
```

## 🔄 Архитектура

### Docker-изолированные MCP серверы
- Каждый сервер запускается в отдельном Docker контейнере
- Автоматическая генерация Dockerfile для каждого сервера
- Автоматическая сборка образов с нужными зависимостями
- Уникальные порты для каждого контейнера
- Restart policy для автоматического перезапуска

### Docker система мониторинга
- Проверка здоровья контейнеров каждые 30 секунд
- Автоматический перезапуск упавших контейнеров
- Максимум 3 попытки перезапуска
- Очистка потерянных контейнеров и образов
- Логирование через Docker API

### Docker менеджер
- Управление жизненным циклом контейнеров
- Автоматическое удаление контейнеров и образов
- Graceful shutdown всех контейнеров
- Статистика использования Docker ресурсов

## 🚦 Мониторинг

Система автоматически отслеживает:
- Состояние процессов серверов
- Доступность HTTP endpoints
- Автоматический перезапуск при сбоях
- Статистика работы в реальном времени

## 🔒 Безопасность

- Изоляция серверов в отдельных процессах
- Ограниченный набор доступных инструментов
- Безопасное выполнение математических выражений
- Автоматическая очистка ресурсов

## 📈 Масштабирование

Для продакшена рекомендуется:
- Использовать PostgreSQL вместо in-memory хранения
- Добавить Redis для кеширования
- Настроить reverse proxy (nginx)
- Добавить аутентификацию и авторизацию
- Использовать Docker для контейнеризации

## 🐛 Отладка

Логи серверов сохраняются в:
- `mcp_servers/{server_id}/{server_name}.log`
- Основные логи в stdout

Для отладки:
```bash
# Запуск в режиме разработки
poetry run uvicorn app.main:app --reload --log-level debug

# Просмотр логов конкретного сервера
tail -f mcp_servers/{server_id}/{server_name}.log
```

## 📝 TODO для продакшена

- [ ] Аутентификация пользователей
- [ ] Персистентное хранилище (PostgreSQL)
- [ ] Rate limiting
- [ ] Метрики и аналитика
- [ ] Docker контейнеризация
- [ ] CI/CD pipeline
- [ ] Backup и восстановление
- [ ] Масштабирование на несколько нод

## 📄 Лицензия

MIT License - см. файл LICENSE

## 🤝 Вклад в проект

1. Fork репозитория
2. Создайте feature branch
3. Внесите изменения
4. Добавьте тесты
5. Создайте Pull Request

---

**Альфа-версия** - для тестирования и демонстрации концепции.
