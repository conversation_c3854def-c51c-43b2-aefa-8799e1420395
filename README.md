# MCP SaaS - Альфа версия

Простой SaaS сервис для создания и управления изолированными MCP (Model Context Protocol) серверами.

## 🚀 Возможности

- **Быстрое развертывание** - создание MCP серверов за секунды
- **Изоляция процессов** - каждый сервер в отдельном процессе с виртуальным окружением
- **Автомониторинг** - автоматическое отслеживание и перезапуск серверов
- **Простой интерфейс** - веб-интерфейс на Bootstrap без сложного ЛК
- **API управление** - REST API для программного управления

## 🛠 Технологический стек

- **Backend**: FastAPI + asyncio
- **MCP SDK**: Официальный Python SDK
- **Frontend**: Bootstrap 5 + Vanilla JS
- **Мониторинг**: Собственная система health checks
- **Изоляция**: Python venv + subprocess

## 📦 Установка

1. **Клонируйте репозиторий:**
```bash
git clone <repo-url>
cd python-mcp-saas
```

2. **Установите Poetry (если не установлен):**
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

3. **Установите зависимости:**
```bash
poetry install
```

4. **Запустите сервер:**
```bash
poetry run python -m app.main
```

Сервис будет доступен по адресу: http://localhost:8000

## 🎯 Использование

### Веб-интерфейс

1. Откройте http://localhost:8000
2. Заполните форму создания сервера:
   - **Название** - уникальное имя сервера
   - **Описание** - краткое описание функций
   - **Инструменты** - список через запятую (calculator, echo, time, random)
3. Нажмите "Создать сервер"
4. Получите URL вашего MCP сервера

### API

**Создание сервера:**
```bash
curl -X POST http://localhost:8000/api/servers \
  -F "name=test-server" \
  -F "description=Тестовый сервер" \
  -F "tools=calculator,echo"
```

**Список серверов:**
```bash
curl http://localhost:8000/api/servers
```

**Удаление сервера:**
```bash
curl -X DELETE http://localhost:8000/api/servers/{server_id}
```

**Проверка здоровья:**
```bash
curl http://localhost:8000/health
```

## 🔧 Доступные инструменты

- **calculator** - математические вычисления
- **echo** - возврат переданного сообщения  
- **time** - текущее время
- **random** - генерация случайных чисел

## 📁 Структура проекта

```
python-mcp-saas/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI приложение
│   ├── models.py            # Модели данных
│   ├── mcp_manager.py       # Менеджер MCP серверов
│   └── health_checker.py    # Система мониторинга
├── templates/
│   ├── base.html           # Базовый шаблон
│   ├── index.html          # Главная страница
│   └── dashboard.html      # Дашборд серверов
├── static/                 # Статические файлы
├── mcp_servers/           # Директория серверов (создается автоматически)
├── pyproject.toml         # Конфигурация Poetry
└── README.md
```

## 🔄 Архитектура

### Изолированные MCP серверы
- Каждый сервер запускается в отдельном Python процессе
- Собственное виртуальное окружение для каждого сервера
- Автоматическая генерация кода на основе выбранных инструментов
- Уникальные порты для каждого сервера

### Система мониторинга
- Проверка здоровья каждые 30 секунд
- Автоматический перезапуск упавших серверов
- Максимум 3 попытки перезапуска
- Логирование всех событий

### Менеджер процессов
- Управление жизненным циклом серверов
- Автоматическое освобождение ресурсов
- Graceful shutdown при остановке

## 🚦 Мониторинг

Система автоматически отслеживает:
- Состояние процессов серверов
- Доступность HTTP endpoints
- Автоматический перезапуск при сбоях
- Статистика работы в реальном времени

## 🔒 Безопасность

- Изоляция серверов в отдельных процессах
- Ограниченный набор доступных инструментов
- Безопасное выполнение математических выражений
- Автоматическая очистка ресурсов

## 📈 Масштабирование

Для продакшена рекомендуется:
- Использовать PostgreSQL вместо in-memory хранения
- Добавить Redis для кеширования
- Настроить reverse proxy (nginx)
- Добавить аутентификацию и авторизацию
- Использовать Docker для контейнеризации

## 🐛 Отладка

Логи серверов сохраняются в:
- `mcp_servers/{server_id}/{server_name}.log`
- Основные логи в stdout

Для отладки:
```bash
# Запуск в режиме разработки
poetry run uvicorn app.main:app --reload --log-level debug

# Просмотр логов конкретного сервера
tail -f mcp_servers/{server_id}/{server_name}.log
```

## 📝 TODO для продакшена

- [ ] Аутентификация пользователей
- [ ] Персистентное хранилище (PostgreSQL)
- [ ] Rate limiting
- [ ] Метрики и аналитика
- [ ] Docker контейнеризация
- [ ] CI/CD pipeline
- [ ] Backup и восстановление
- [ ] Масштабирование на несколько нод

## 📄 Лицензия

MIT License - см. файл LICENSE

## 🤝 Вклад в проект

1. Fork репозитория
2. Создайте feature branch
3. Внесите изменения
4. Добавьте тесты
5. Создайте Pull Request

---

**Альфа-версия** - для тестирования и демонстрации концепции.
