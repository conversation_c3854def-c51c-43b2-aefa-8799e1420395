{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<!-- Dashboard Header -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="display-5 fw-bold">
                    <i class="bi bi-speedometer2"></i> Дашборд серверов
                </h1>
                <p class="lead text-muted">Управление и мониторинг ваших MCP серверов</p>
            </div>
            <div class="col-auto">
                <a href="/" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Создать новый
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Stats Cards -->
<section class="py-4">
    <div class="container">
        <div class="row g-4">
            <div class="col-md-3">
                <div class="card text-center border-primary">
                    <div class="card-body">
                        <i class="bi bi-server text-primary" style="font-size: 2rem;"></i>
                        <h4 class="mt-2" id="total-servers">{{ servers|length }}</h4>
                        <p class="text-muted mb-0">Всего серверов</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-success">
                    <div class="card-body">
                        <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                        <h4 class="mt-2" id="running-servers">
                            {{ servers|selectattr("status", "equalto", "running")|list|length }}
                        </h4>
                        <p class="text-muted mb-0">Работающих</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-warning">
                    <div class="card-body">
                        <i class="bi bi-arrow-clockwise text-warning" style="font-size: 2rem;"></i>
                        <h4 class="mt-2" id="restarted-servers">
                            {{ servers|selectattr("status", "equalto", "restarted")|list|length }}
                        </h4>
                        <p class="text-muted mb-0">Перезапущенных</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-danger">
                    <div class="card-body">
                        <i class="bi bi-x-circle text-danger" style="font-size: 2rem;"></i>
                        <h4 class="mt-2" id="failed-servers">
                            {{ servers|selectattr("status", "equalto", "failed")|list|length }}
                        </h4>
                        <p class="text-muted mb-0">Неработающих</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Servers List -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul"></i> Список серверов
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshServers()">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                    </div>
                    <div class="card-body">
                        {% if servers %}
                            <div class="row g-4" id="servers-container">
                                {% for server in servers %}
                                <div class="col-lg-6">
                                    <div class="card server-card {% if server.status == 'failed' %}failed{% endif %}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h6 class="card-title mb-0">
                                                    <i class="bi bi-server"></i> {{ server.name }}
                                                </h6>
                                                <span class="badge status-badge 
                                                    {% if server.status == 'running' %}bg-success
                                                    {% elif server.status == 'restarted' %}bg-warning
                                                    {% elif server.status == 'failed' %}bg-danger
                                                    {% else %}bg-secondary{% endif %}">
                                                    {{ server.status }}
                                                </span>
                                            </div>
                                            
                                            <p class="card-text text-muted small mb-2">
                                                {{ server.description or "Без описания" }}
                                            </p>
                                            
                                            <div class="row text-center mb-3">
                                                <div class="col-4">
                                                    <small class="text-muted">Порт</small>
                                                    <div class="fw-bold">{{ server.port }}</div>
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted">Инструменты</small>
                                                    <div class="fw-bold">{{ server.tools|length }}</div>
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted">Создан</small>
                                                    <div class="fw-bold">
                                                        {{ server.created_at.strftime('%H:%M') }}
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <small class="text-muted">Доступные инструменты:</small>
                                                <div>
                                                    {% for tool in server.tools %}
                                                        <span class="badge bg-light text-dark me-1">{{ tool }}</span>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex gap-2">
                                                <a href="{{ server.url }}" target="_blank" 
                                                   class="btn btn-outline-primary btn-sm flex-fill">
                                                    <i class="bi bi-box-arrow-up-right"></i> Открыть
                                                </a>
                                                <button class="btn btn-outline-danger btn-sm" 
                                                        onclick="deleteServer('{{ server.server_id }}', '{{ server.name }}')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="bi bi-server text-muted" style="font-size: 4rem;"></i>
                                <h5 class="mt-3 text-muted">Нет активных серверов</h5>
                                <p class="text-muted">Создайте свой первый MCP сервер</p>
                                <a href="/" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i> Создать сервер
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Health Status Modal -->
<div class="modal fade" id="healthModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-heart-pulse"></i> Статус мониторинга
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="health-status-content">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Обновление списка серверов
async function refreshServers() {
    try {
        const response = await fetch('/api/servers');
        const data = await response.json();
        
        // Обновляем статистику
        document.getElementById('total-servers').textContent = data.servers.length;
        document.getElementById('running-servers').textContent = 
            data.servers.filter(s => s.status === 'running').length;
        document.getElementById('restarted-servers').textContent = 
            data.servers.filter(s => s.status === 'restarted').length;
        document.getElementById('failed-servers').textContent = 
            data.servers.filter(s => s.status === 'failed').length;
        
        showAlert('Список серверов обновлен', 'info');
    } catch (error) {
        showAlert('Ошибка обновления: ' + error.message, 'danger');
    }
}

// Удаление сервера
async function deleteServer(serverId, serverName) {
    if (!confirm(`Вы уверены, что хотите удалить сервер "${serverName}"?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/servers/${serverId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert(`Сервер "${serverName}" удален`, 'success');
            // Перезагружаем страницу для обновления списка
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('Ошибка удаления сервера', 'danger');
        }
    } catch (error) {
        showAlert('Ошибка сети: ' + error.message, 'danger');
    }
}

// Показать статус мониторинга
async function showHealthStatus() {
    const modal = new bootstrap.Modal(document.getElementById('healthModal'));
    modal.show();
    
    try {
        const response = await fetch('/health');
        const data = await response.json();
        
        document.getElementById('health-status-content').innerHTML = `
            <div class="row text-center">
                <div class="col-6">
                    <h4 class="text-success">${data.active_servers}</h4>
                    <p class="text-muted">Активных серверов</p>
                </div>
                <div class="col-6">
                    <h4 class="text-primary">${data.version}</h4>
                    <p class="text-muted">Версия</p>
                </div>
            </div>
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> Система работает нормально
            </div>
        `;
    } catch (error) {
        document.getElementById('health-status-content').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> Ошибка получения статуса: ${error.message}
            </div>
        `;
    }
}

// Автообновление каждые 30 секунд
setInterval(refreshServers, 30000);
</script>
{% endblock %}
