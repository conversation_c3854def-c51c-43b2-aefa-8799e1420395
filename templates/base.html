<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MCP SaaS{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .server-card {
            border-left: 4px solid #28a745;
        }
        
        .server-card.failed {
            border-left-color: #dc3545;
        }
        
        .status-badge {
            font-size: 0.8em;
        }
        
        footer {
            background-color: #f8f9fa;
            margin-top: 50px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-server"></i> MCP SaaS
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Главная</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">Дашборд</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/api/servers" target="_blank">API</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 MCP SaaS. Альфа-версия.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <small class="text-muted">
                            Powered by FastAPI + MCP SDK
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Показать уведомления
        function showAlert(message, type = 'success') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container');
            if (container) {
                container.insertBefore(alertDiv, container.firstChild);
                
                // Автоматически скрыть через 5 секунд
                setTimeout(() => {
                    alertDiv.remove();
                }, 5000);
            }
        }
        
        // Обновить статус серверов
        function updateServerStatus() {
            fetch('/api/servers')
                .then(response => response.json())
                .then(data => {
                    // Обновляем счетчики на дашборде
                    const totalElement = document.getElementById('total-servers');
                    if (totalElement) {
                        totalElement.textContent = data.servers.length;
                    }
                })
                .catch(error => console.error('Ошибка обновления статуса:', error));
        }
        
        // Обновляем статус каждые 30 секунд
        setInterval(updateServerStatus, 30000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
