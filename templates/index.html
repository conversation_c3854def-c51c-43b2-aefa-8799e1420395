{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    Создайте свой MCP сервер за минуты
                </h1>
                <p class="lead mb-4">
                    Простой и быстрый способ развернуть изолированные MCP серверы 
                    с автоматическим мониторингом и управлением.
                </p>
                <a href="#create-server" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle"></i> Создать сервер
                </a>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="bi bi-server" style="font-size: 8rem; opacity: 0.8;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col">
                <h2 class="fw-bold">Возможности платформы</h2>
                <p class="text-muted">Все что нужно для работы с MCP серверами</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-lightning-charge text-primary" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">Быстрое развертывание</h5>
                        <p class="card-text">
                            Создавайте MCP серверы за секунды с автоматической настройкой 
                            виртуального окружения и зависимостей.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-shield-check text-success" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">Изоляция процессов</h5>
                        <p class="card-text">
                            Каждый сервер работает в отдельном процессе с собственным 
                            виртуальным окружением для максимальной безопасности.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-graph-up text-info" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">Автомониторинг</h5>
                        <p class="card-text">
                            Система автоматически отслеживает состояние серверов 
                            и перезапускает их при необходимости.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Create Server Section -->
<section id="create-server" class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-plus-circle"></i> Создать новый MCP сервер
                        </h4>
                    </div>
                    <div class="card-body">
                        <form id="create-server-form">
                            <div class="mb-3">
                                <label for="name" class="form-label">Название сервера *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       placeholder="Мой MCP сервер" required>
                                <div class="form-text">Уникальное имя для вашего сервера</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Описание</label>
                                <textarea class="form-control" id="description" name="description" 
                                          rows="3" placeholder="Описание функций сервера..."></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="tools" class="form-label">Инструменты *</label>
                                <input type="text" class="form-control" id="tools" name="tools" 
                                       placeholder="calculator, echo, time, random" required>
                                <div class="form-text">
                                    Список инструментов через запятую. Доступные: 
                                    <code>calculator</code>, <code>echo</code>, <code>time</code>, <code>random</code>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-rocket"></i> Создать сервер
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="card border-0">
                    <div class="card-body">
                        <h3 class="text-primary" id="total-servers">0</h3>
                        <p class="text-muted">Активных серверов</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0">
                    <div class="card-body">
                        <h3 class="text-success">99.9%</h3>
                        <p class="text-muted">Время работы</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0">
                    <div class="card-body">
                        <h3 class="text-info">&lt; 30с</h3>
                        <p class="text-muted">Время развертывания</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('create-server-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Показываем индикатор загрузки
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Создание...';
    submitBtn.disabled = true;
    
    try {
        const response = await fetch('/api/servers', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert(`✅ ${result.message}<br><strong>URL:</strong> <a href="${result.url}" target="_blank">${result.url}</a>`, 'success');
            this.reset();
            updateServerStatus();
        } else {
            showAlert(`❌ Ошибка: ${result.message || 'Неизвестная ошибка'}`, 'danger');
        }
    } catch (error) {
        showAlert(`❌ Ошибка сети: ${error.message}`, 'danger');
    } finally {
        // Восстанавливаем кнопку
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Загружаем статистику при загрузке страницы
document.addEventListener('DOMContentLoaded', updateServerStatus);
</script>
{% endblock %}
