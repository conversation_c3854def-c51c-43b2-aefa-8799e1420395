#!/usr/bin/env python3
"""
Автоматически сгенерированный MCP сервер: test
Описание: test
"""
import asyncio
import sys
import os
import logging
from mcp.server.fastmcp import FastMCP

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='[test] %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler('test.log'),
        logging.StreamHandler()
    ]
)

# Создаем MCP сервер
mcp = FastMCP("test")


@mcp.tool()
def echo(message: str) -> str:
    """Эхо - возвращает переданное сообщение"""
    return f"Эхо: {message}"



@mcp.resource("info://server")
def get_server_info() -> str:
    """Информация о сервере"""
    return f"""
Сервер: test
Описание: test
Доступные инструменты: echo
"""

if __name__ == "__main__":
    port = int(os.getenv("MCP_SERVER_PORT", 8001))
    logging.info(f"Запуск MCP сервера 'test' на порту {port}")

    # Запускаем сервер через встроенный метод run
    mcp.run(transport="sse", host="127.0.0.1", port=port)
