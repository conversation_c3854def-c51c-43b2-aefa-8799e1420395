#!/usr/bin/env python3
"""
Автоматически сгенерированный MCP сервер: working-server
Описание: Рабочий сервер
"""
import asyncio
import sys
import os
import logging
from mcp.server.fastmcp import FastMCP

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='[working-server] %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler('working-server.log'),
        logging.StreamHandler()
    ]
)

# Создаем MCP сервер
mcp = FastMCP("working-server")


@mcp.tool()
def echo(message: str) -> str:
    """Эхо - возвращает переданное сообщение"""
    return f"Эхо: {message}"



@mcp.resource("info://server")
def get_server_info() -> str:
    """Информация о сервере"""
    return f"""
Сервер: working-server
Описание: Рабочий сервер
Доступные инструменты: echo
"""

async def main():
    """Запуск сервера"""
    port = int(os.getenv("MCP_SERVER_PORT", 8001))
    
    try:
        logging.info(f"Запуск MCP сервера 'working-server' на порту {port}")
        # Запускаем сервер через uvicorn
        import uvicorn
        config = uvicorn.Config(
            mcp.sse_app(),
            host="127.0.0.1",
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
    except Exception as e:
        logging.error(f"Ошибка сервера: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
