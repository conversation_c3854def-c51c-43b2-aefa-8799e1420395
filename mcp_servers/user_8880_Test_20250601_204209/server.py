#!/usr/bin/env python3
"""
Автоматически сгенерированный MCP сервер: Test
Описание: тест описание сервера
"""
import asyncio
import sys
import os
import logging
from mcp.server.fastmcp import FastMCP

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='[Test] %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler('Test.log'),
        logging.StreamHandler()
    ]
)

# Создаем MCP сервер
mcp = FastMCP("Test")


@mcp.tool()
def calculate(expression: str) -> str:
    """Калькулятор для вычисления математических выражений"""
    try:
        # Безопасное вычисление только математических операций
        allowed_chars = set('0123456789+-*/().')
        if not all(c in allowed_chars or c.isspace() for c in expression):
            return "Ошибка: недопустимые символы в выражении"
        
        result = eval(expression)
        return f"Результат: {result}"
    except Exception as e:
        return f"Ошибка вычисления: {str(e)}"


@mcp.tool()
def echo(message: str) -> str:
    """Эхо - возвращает переданное сообщение"""
    return f"Эхо: {message}"


@mcp.tool()
def get_current_time() -> str:
    """Возвращает текущее время"""
    from datetime import datetime
    return f"Текущее время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"


@mcp.tool()
def random_number(min_val: int = 1, max_val: int = 100) -> str:
    """Генерирует случайное число в заданном диапазоне"""
    import random
    number = random.randint(min_val, max_val)
    return f"Случайное число от {min_val} до {max_val}: {number}"



@mcp.resource("info://server")
def get_server_info() -> str:
    """Информация о сервере"""
    return f"""
Сервер: Test
Описание: тест описание сервера
Доступные инструменты: calculator, echo, time, random
"""

async def main():
    """Запуск сервера"""
    port = int(os.getenv("MCP_SERVER_PORT", 8001))
    
    try:
        logging.info(f"Запуск MCP сервера 'Test' на порту {port}")
        # Запускаем сервер через uvicorn
        import uvicorn
        config = uvicorn.Config(
            mcp.sse_app(),
            host="127.0.0.1",
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
    except Exception as e:
        logging.error(f"Ошибка сервера: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
