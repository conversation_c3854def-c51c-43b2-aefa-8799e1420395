#!/usr/bin/env python3
"""
Автоматически сгенерированный MCP сервер: test-server
Описание: Тестовый MCP сервер
"""
import asyncio
import sys
import os
import logging
from mcp.server.fastmcp import FastMCP

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='[test-server] %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler('test-server.log'),
        logging.StreamHandler()
    ]
)

# Создаем MCP сервер
mcp = FastMCP("test-server")


@mcp.tool()
def calculate(expression: str) -> str:
    """Калькулятор для вычисления математических выражений"""
    try:
        # Безопасное вычисление только математических операций
        allowed_chars = set('0123456789+-*/().')
        if not all(c in allowed_chars or c.isspace() for c in expression):
            return "Ошибка: недопустимые символы в выражении"
        
        result = eval(expression)
        return f"Результат: {result}"
    except Exception as e:
        return f"Ошибка вычисления: {str(e)}"


@mcp.tool()
def echo(message: str) -> str:
    """Эхо - возвращает переданное сообщение"""
    return f"Эхо: {message}"



@mcp.resource("info://server")
def get_server_info() -> str:
    """Информация о сервере"""
    return f"""
Сервер: test-server
Описание: Тестовый MCP сервер
Доступные инструменты: calculator, echo
"""

async def main():
    """Запуск сервера"""
    port = int(os.getenv("MCP_SERVER_PORT", 8001))
    
    try:
        logging.info(f"Запуск MCP сервера 'test-server' на порту {port}")
        # Запускаем сервер через uvicorn
        import uvicorn
        config = uvicorn.Config(
            mcp.sse_app(),
            host="127.0.0.1",
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
    except Exception as e:
        logging.error(f"Ошибка сервера: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
