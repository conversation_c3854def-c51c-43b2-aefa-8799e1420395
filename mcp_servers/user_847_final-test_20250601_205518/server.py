#!/usr/bin/env python3
"""
Автоматически сгенерированный MCP сервер: final-test
Описание: Финальный тест
"""
import asyncio
import sys
import os
import logging
from mcp.server.fastmcp import FastMCP

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='[final-test] %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler('final-test.log'),
        logging.StreamHandler()
    ]
)

# Создаем MCP сервер
mcp = FastMCP("final-test")


@mcp.tool()
def echo(message: str) -> str:
    """Эхо - возвращает переданное сообщение"""
    return f"Эхо: {message}"


@mcp.tool()
def calculate(expression: str) -> str:
    """Калькулятор для вычисления математических выражений"""
    try:
        # Безопасное вычисление только математических операций
        allowed_chars = set('0123456789+-*/().')
        if not all(c in allowed_chars or c.isspace() for c in expression):
            return "Ошибка: недопустимые символы в выражении"
        
        result = eval(expression)
        return f"Результат: {result}"
    except Exception as e:
        return f"Ошибка вычисления: {str(e)}"



@mcp.resource("info://server")
def get_server_info() -> str:
    """Информация о сервере"""
    return f"""
Сервер: final-test
Описание: Финальный тест
Доступные инструменты: echo, calculator
"""

if __name__ == "__main__":
    port = int(os.getenv("MCP_SERVER_PORT", 8001))
    logging.info(f"Запуск MCP сервера 'final-test' на порту {port}")

    # Запускаем сервер через встроенный метод run
    mcp.run(transport="sse", host="127.0.0.1", port=port)
