# Dockerfile для MCP сервера: docker-test
FROM python:3.11-slim

# Устанавливаем рабочую директорию
WORKDIR /app

# Копируем файлы зависимостей
COPY requirements.txt .

# Устанавливаем зависимости
RUN pip install --no-cache-dir -r requirements.txt

# Копируем код сервера
COPY server.py .

# Открываем порт
EXPOSE 8000

# Устанавливаем переменные окружения
ENV PYTHONUNBUFFERED=1
ENV MCP_SERVER_NAME="docker-test"

# Запускаем сервер
CMD ["python", "server.py"]
