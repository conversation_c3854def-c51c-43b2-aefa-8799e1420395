#!/usr/bin/env python3
"""
Автоматически сгенерированный MCP сервер: docker-test
Описание: Тест Docker сервера
Запускается в Docker контейнере
"""
import asyncio
import sys
import os
import logging
from mcp.server.fastmcp import FastMCP

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='[docker-test] %(levelname)s: %(message)s',
    handlers=[logging.StreamHandler()]
)

logger = logging.getLogger(__name__)

# Создаем MCP сервер
mcp = FastMCP("docker-test")


@mcp.tool()
def echo(message: str) -> str:
    """Эхо - возвращает переданное сообщение"""
    return f"Эхо из контейнера: {message}"


@mcp.tool()
def calculate(expression: str) -> str:
    """Калькулятор для вычисления математических выражений"""
    try:
        # Безопасное вычисление только математических операций
        allowed_chars = set('0123456789+-*/().')
        if not all(c in allowed_chars or c.isspace() for c in expression):
            return "Ошибка: недопустимые символы в выражении"
        
        result = eval(expression)
        return f"Результат: {result}"
    except Exception as e:
        return f"Ошибка вычисления: {str(e)}"



@mcp.resource("info://server")
def get_server_info() -> str:
    """Информация о сервере"""
    return f"""
Сервер: docker-test
Описание: Тест Docker сервера
Доступные инструменты: echo, calculator
Контейнер: {os.getenv("HOSTNAME", "unknown")}
"""

@mcp.resource("health://check")
def health_check() -> str:
    """Проверка здоровья сервера"""
    return "OK"

if __name__ == "__main__":
    port = int(os.getenv("MCP_SERVER_PORT", 8000))
    logger.info(f"Запуск MCP сервера 'docker-test' на порту {port} в контейнере")
    
    # Запускаем сервер
    mcp.run(transport="sse", host="0.0.0.0", port=port)
