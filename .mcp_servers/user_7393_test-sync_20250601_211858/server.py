#!/usr/bin/env python3
"""
Автоматически сгенерированный MCP сервер: test-sync
Описание: Тест синхронных файлов
Запускается в Docker контейнере
"""
import asyncio
import sys
import os
import logging
from mcp.server.fastmcp import FastMCP

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='[test-sync] %(levelname)s: %(message)s',
    handlers=[logging.StreamHandler()]
)

logger = logging.getLogger(__name__)

# Создаем MCP сервер
mcp = FastMCP("test-sync")


@mcp.tool()
def echo(message: str) -> str:
    """Эхо - возвращает переданное сообщение"""
    return f"Эхо из контейнера: {message}"



@mcp.resource("info://server")
def get_server_info() -> str:
    """Информация о сервере"""
    return f"""
Сервер: test-sync
Описание: Тест синхронных файлов
Доступные инструменты: echo
Контейнер: {os.getenv("HOSTNAME", "unknown")}
"""

@mcp.resource("health://check")
def health_check() -> str:
    """Проверка здоровья сервера"""
    return "OK"

if __name__ == "__main__":
    port = int(os.getenv("MCP_SERVER_PORT", 3000))
    logger.info(f"Запуск MCP сервера 'test-sync' на порту {port} в контейнере")

    # Запускаем сервер (FastMCP.run не поддерживает host параметр)
    mcp.run(transport="sse", port=port)
