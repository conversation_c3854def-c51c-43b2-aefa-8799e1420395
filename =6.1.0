Collecting docker
  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)
Collecting requests>=2.26.0 (from docker)
  Using cached requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)
Collecting urllib3>=1.26.0 (from docker)
  Using cached urllib3-2.4.0-py3-none-any.whl.metadata (6.5 kB)
Collecting charset-normalizer<4,>=2 (from requests>=2.26.0->docker)
  Using cached charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (35 kB)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.12/site-packages (from requests>=2.26.0->docker) (3.10)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.12/site-packages (from requests>=2.26.0->docker) (2025.4.26)
Downloading docker-7.1.0-py3-none-any.whl (147 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 147.8/147.8 kB 257.1 kB/s eta 0:00:00
Using cached requests-2.32.3-py3-none-any.whl (64 kB)
Using cached urllib3-2.4.0-py3-none-any.whl (128 kB)
Using cached charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (148 kB)
Installing collected packages: urllib3, charset-normalizer, requests, docker
Successfully installed charset-normalizer-3.4.2 docker-7.1.0 requests-2.32.3 urllib3-2.4.0
